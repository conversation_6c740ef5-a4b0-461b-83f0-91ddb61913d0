export interface Subject {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
}

export interface TimerSession {
  id: string;
  subjectId?: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  mode: 'stopwatch' | 'pomodoro';
  isBreak?: boolean;
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  targetDate: Date;
  subjectId?: string;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  createdAt: Date;
}

export interface PomodoroSettings {
  workDuration: number; // in minutes
  shortBreakDuration: number; // in minutes
  longBreakDuration: number; // in minutes
  sessionsUntilLongBreak: number;
}