import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Clock,
  Timer as TimerIcon,
  Settings as SettingsIcon,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { useTimer } from '@/hooks/useTimer';

const { width } = Dimensions.get('window');

export default function TimerScreen() {
  const {
    isRunning,
    time,
    mode,
    currentSubject,
    pomodoroPhase,
    pomodoroSession,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    switchMode,
    setCurrentSubject,
    formatTime,
    getTotalTimeToday,
  } = useTimer();

  const [showSettings, setShowSettings] = useState(false);
  const pulseScale = useSharedValue(1);
  const progressRotation = useSharedValue(0);

  useEffect(() => {
    if (isRunning) {
      pulseScale.value = withRepeat(
        withTiming(1.05, { duration: 1000 }),
        -1,
        true
      );
    } else {
      pulseScale.value = withSpring(1);
    }
  }, [isRunning]);

  const timerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  const getTimerProgress = () => {
    if (mode === 'stopwatch') return 0;
    
    const totalTime = pomodoroPhase === 'work' ? 25 * 60 : 5 * 60; // Simplified for demo
    return ((totalTime - time) / totalTime) * 100;
  };

  const getPhaseText = () => {
    if (mode === 'stopwatch') return 'Stopwatch';
    return pomodoroPhase === 'work' ? `Work Session ${pomodoroSession}` : 'Break Time';
  };

  const getPhaseColor = () => {
    if (mode === 'stopwatch') return ['#6366F1', '#8B5CF6'];
    return pomodoroPhase === 'work' ? ['#EF4444', '#F87171'] : ['#10B981', '#34D399'];
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <IsotopeLogo size="medium" />
          <TouchableOpacity 
            style={styles.settingsButton}
            onPress={() => setShowSettings(!showSettings)}
          >
            <SettingsIcon size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Mode Selector */}
      <View style={styles.modeSection}>
        <View style={styles.modeSelector}>
          <TouchableOpacity
            style={[
              styles.modeButton,
              mode === 'stopwatch' && styles.modeButtonActive,
            ]}
            onPress={() => switchMode('stopwatch')}
          >
            <Clock size={20} color={mode === 'stopwatch' ? '#FFFFFF' : '#6B7280'} />
            <Text
              style={[
                styles.modeText,
                mode === 'stopwatch' && styles.modeTextActive,
              ]}
            >
              Stopwatch
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.modeButton,
              mode === 'pomodoro' && styles.modeButtonActive,
            ]}
            onPress={() => switchMode('pomodoro')}
          >
            <TimerIcon size={20} color={mode === 'pomodoro' ? '#FFFFFF' : '#6B7280'} />
            <Text
              style={[
                styles.modeText,
                mode === 'pomodoro' && styles.modeTextActive,
              ]}
            >
              Pomodoro
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Subject Picker */}
      <View style={styles.subjectSection}>
        <SubjectPicker
          selectedSubject={currentSubject}
          onSelectSubject={setCurrentSubject}
        />
      </View>

      {/* Main Timer */}
      <View style={styles.timerSection}>
        <Animated.View style={[styles.timerContainer, timerAnimatedStyle]}>
          <LinearGradient
            colors={getPhaseColor()}
            style={styles.timerGradient}
          >
            <View style={styles.timerInner}>
              <Text style={styles.phaseText}>{getPhaseText()}</Text>
              <Text style={styles.timerText}>{formatTime(time)}</Text>
              
              {mode === 'pomodoro' && (
                <View style={styles.progressRing}>
                  <View style={styles.progressTrack}>
                    <View
                      style={[
                        styles.progressFill,
                        {
                          width: `${getTimerProgress()}%`,
                          backgroundColor: pomodoroPhase === 'work' ? '#EF4444' : '#10B981',
                        },
                      ]}
                    />
                  </View>
                </View>
              )}
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Timer Controls */}
        <View style={styles.timerControls}>
          <TouchableOpacity style={styles.controlButton} onPress={resetTimer}>
            <RotateCcw size={24} color="#6366F1" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.playButton} onPress={isRunning ? pauseTimer : startTimer}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.playGradient}
            >
              {isRunning ? (
                <Pause size={32} color="#FFFFFF" />
              ) : (
                <Play size={32} color="#FFFFFF" />
              )}
            </LinearGradient>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.controlButton} onPress={stopTimer}>
            <Square size={24} color="#EF4444" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Today's Stats */}
      <View style={styles.statsSection}>
        <Text style={styles.sectionTitle}>Today's Progress</Text>
        
        <View style={styles.statsCard}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{formatTime(getTotalTimeToday())}</Text>
            <Text style={styles.statLabel}>Total Time</Text>
          </View>
          
          <View style={styles.statDivider} />
          
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {currentSubject ? currentSubject.name : 'No Subject'}
            </Text>
            <Text style={styles.statLabel}>Current Subject</Text>
          </View>
        </View>
      </View>

      {/* Quick Tips */}
      <View style={styles.tipsSection}>
        <Text style={styles.sectionTitle}>Tips</Text>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>
            {mode === 'stopwatch' ? '⏱️ Stopwatch Mode' : '🍅 Pomodoro Technique'}
          </Text>
          <Text style={styles.tipText}>
            {mode === 'stopwatch' 
              ? 'Perfect for open-ended study sessions. Track your time without pressure.'
              : 'Work for 25 minutes, then take a 5-minute break. After 4 sessions, take a longer break.'
            }
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  settingsButton: {
    width: 40,
    height: 40,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modeSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  modeSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
  },
  modeButtonActive: {
    backgroundColor: '#6366F1',
  },
  modeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modeTextActive: {
    color: '#FFFFFF',
  },
  subjectSection: {
    paddingHorizontal: 24,
  },
  timerSection: {
    alignItems: 'center',
    marginVertical: 32,
  },
  timerContainer: {
    marginBottom: 32,
  },
  timerGradient: {
    width: width - 80,
    height: width - 80,
    borderRadius: (width - 80) / 2,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  timerInner: {
    width: '85%',
    height: '85%',
    backgroundColor: '#FFFFFF',
    borderRadius: (width - 120) / 2,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  phaseText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  timerText: {
    fontSize: 48,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    textAlign: 'center',
  },
  progressRing: {
    width: '80%',
    alignItems: 'center',
    marginTop: 16,
  },
  progressTrack: {
    width: '100%',
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  timerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 24,
  },
  controlButton: {
    width: 56,
    height: 56,
    backgroundColor: '#FFFFFF',
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  playButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  playGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  statsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 20,
  },
  tipsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  tipCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
});