import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  VolumeX, 
  Smartphone,
  Clock,
  Zap,
  Target,
  Coffee
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  useAnimatedProps,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

const { width } = Dimensions.get('window');

interface FocusSession {
  id: string;
  duration: number;
  breaks: number;
  completed: boolean;
  efficiency: number;
}

interface SoundOption {
  id: string;
  name: string;
  icon: string;
  active: boolean;
}

export default function FocusScreen() {
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes
  const [breakTime, setBreakTime] = useState(5 * 60); // 5 minutes
  const [isBreak, setIsBreak] = useState(false);
  const [currentCycle, setCurrentCycle] = useState(1);
  const [totalCycles] = useState(4);
  const [focusStreak, setFocusStreak] = useState(12);
  const [distractionsBlocked, setDistractionsBlocked] = useState(23);

  const [soundOptions, setSoundOptions] = useState<SoundOption[]>([
    { id: '1', name: 'Rain', icon: '🌧️', active: false },
    { id: '2', name: 'Forest', icon: '🌲', active: true },
    { id: '3', name: 'Ocean', icon: '🌊', active: false },
    { id: '4', name: 'Silence', icon: '🔇', active: false },
  ]);

  const pulseScale = useSharedValue(1);
  const progressScale = useSharedValue(1);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isSessionActive && !isPaused && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      handleTimerComplete();
    }
    return () => clearInterval(interval);
  }, [isSessionActive, isPaused, timeLeft]);

  useEffect(() => {
    if (isSessionActive) {
      pulseScale.value = withRepeat(
        withTiming(1.05, { duration: 1000 }),
        -1,
        true
      );
    } else {
      pulseScale.value = withSpring(1);
    }
  }, [isSessionActive]);

  const handleTimerComplete = () => {
    if (!isBreak) {
      // Focus session completed
      setIsBreak(true);
      setTimeLeft(breakTime);
      if (currentCycle < totalCycles) {
        // Short break
        setTimeLeft(5 * 60);
      } else {
        // Long break after 4 cycles
        setTimeLeft(15 * 60);
      }
    } else {
      // Break completed
      setIsBreak(false);
      if (currentCycle < totalCycles) {
        setCurrentCycle(prev => prev + 1);
        setTimeLeft(25 * 60);
      } else {
        // All cycles completed
        setIsSessionActive(false);
        setCurrentCycle(1);
        setTimeLeft(25 * 60);
        setFocusStreak(prev => prev + 1);
      }
    }
  };

  const startSession = () => {
    setIsSessionActive(true);
    setIsPaused(false);
  };

  const pauseSession = () => {
    setIsPaused(!isPaused);
  };

  const stopSession = () => {
    setIsSessionActive(false);
    setIsPaused(false);
    setIsBreak(false);
    setCurrentCycle(1);
    setTimeLeft(25 * 60);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const toggleSound = (soundId: string) => {
    setSoundOptions(prev => 
      prev.map(sound => ({
        ...sound,
        active: sound.id === soundId ? !sound.active : false
      }))
    );
  };

  const getProgressPercentage = () => {
    const totalTime = isBreak ? breakTime : 25 * 60;
    return ((totalTime - timeLeft) / totalTime) * 100;
  };

  const timerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Focus Mode</Text>
          <Text style={styles.subtitle}>
            {isBreak ? 'Take a break!' : 'Time to focus'}
          </Text>
        </View>
      </LinearGradient>

      {/* Session Stats */}
      <View style={styles.statsSection}>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Zap size={20} color="#6B46C1" />
            <Text style={styles.statValue}>{focusStreak}</Text>
            <Text style={styles.statLabel}>Focus Streak</Text>
          </View>
          
          <View style={styles.statCard}>
            <Clock size={20} color="#10B981" />
            <Text style={styles.statValue}>{currentCycle}/{totalCycles}</Text>
            <Text style={styles.statLabel}>Cycle</Text>
          </View>
          
          <View style={styles.statCard}>
            <Smartphone size={20} color="#EF4444" />
            <Text style={styles.statValue}>{distractionsBlocked}</Text>
            <Text style={styles.statLabel}>Blocked</Text>
          </View>
        </View>
      </View>

      {/* Main Timer */}
      <View style={styles.timerSection}>
        <Animated.View style={[styles.timerContainer, timerAnimatedStyle]}>
          <LinearGradient
            colors={isBreak ? ['#10B981', '#34D399'] : ['#6B46C1', '#7C3AED']}
            style={styles.timerGradient}
          >
            <View style={styles.timerInner}>
              <Text style={styles.timerText}>{formatTime(timeLeft)}</Text>
              <Text style={styles.timerLabel}>
                {isBreak ? 'Break Time' : 'Focus Time'}
              </Text>
              
              {/* Progress Ring */}
              <View style={styles.progressRing}>
                <View style={styles.progressTrack}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${getProgressPercentage()}%`,
                        backgroundColor: isBreak ? '#10B981' : '#6B46C1',
                      },
                    ]}
                  />
                </View>
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Timer Controls */}
        <View style={styles.timerControls}>
          {!isSessionActive ? (
            <TouchableOpacity style={styles.startButton} onPress={startSession}>
              <LinearGradient
                colors={['#6B46C1', '#7C3AED']}
                style={styles.startGradient}
              >
                <Play size={32} color="#FFFFFF" />
                <Text style={styles.startText}>Start Focus</Text>
              </LinearGradient>
            </TouchableOpacity>
          ) : (
            <View style={styles.activeControls}>
              <TouchableOpacity style={styles.controlButton} onPress={pauseSession}>
                {isPaused ? (
                  <Play size={28} color="#FFFFFF" />
                ) : (
                  <Pause size={28} color="#FFFFFF" />
                )}
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.stopButton} onPress={stopSession}>
                <Square size={28} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      {/* Focus Sounds */}
      <View style={styles.soundsSection}>
        <Text style={styles.sectionTitle}>Focus Sounds</Text>
        <View style={styles.soundGrid}>
          {soundOptions.map((sound) => (
            <TouchableOpacity
              key={sound.id}
              style={[
                styles.soundCard,
                sound.active && styles.soundCardActive,
              ]}
              onPress={() => toggleSound(sound.id)}
            >
              <Text style={styles.soundIcon}>{sound.icon}</Text>
              <Text
                style={[
                  styles.soundName,
                  sound.active && styles.soundNameActive,
                ]}
              >
                {sound.name}
              </Text>
              {sound.active && (
                <View style={styles.activeBadge}>
                  <Volume2 size={16} color="#FFFFFF" />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Distraction Blocking */}
      <View style={styles.blockingSection}>
        <Text style={styles.sectionTitle}>Distraction Blocking</Text>
        
        <View style={styles.blockingCard}>
          <View style={styles.blockingHeader}>
            <Smartphone size={24} color="#EF4444" />
            <Text style={styles.blockingTitle}>Active Blocking</Text>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>ON</Text>
            </View>
          </View>
          
          <Text style={styles.blockingDescription}>
            Social media, gaming, and entertainment apps are blocked during focus sessions.
          </Text>
          
          <View style={styles.blockedApps}>
            <Text style={styles.blockedTitle}>Recently Blocked:</Text>
            <View style={styles.blockedList}>
              <Text style={styles.blockedApp}>📱 Instagram (5 attempts)</Text>
              <Text style={styles.blockedApp}>🎮 YouTube (3 attempts)</Text>
              <Text style={styles.blockedApp}>💬 WhatsApp (2 attempts)</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Break Activities */}
      {isBreak && (
        <View style={styles.breakSection}>
          <Text style={styles.sectionTitle}>Break Activities</Text>
          
          <View style={styles.activityGrid}>
            <TouchableOpacity style={styles.activityCard}>
              <Coffee size={24} color="#F59E0B" />
              <Text style={styles.activityText}>Get a drink</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.activityCard}>
              <Target size={24} color="#10B981" />
              <Text style={styles.activityText}>Stretch</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Quick Tips */}
      <View style={styles.tipsSection}>
        <Text style={styles.sectionTitle}>Focus Tips</Text>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>💡 Stay Hydrated</Text>
          <Text style={styles.tipText}>
            Keep water nearby to maintain focus and avoid dehydration breaks.
          </Text>
        </View>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>🧘 Deep Breathing</Text>
          <Text style={styles.tipText}>
            Take 3 deep breaths before starting to center your mind.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  timerSection: {
    alignItems: 'center',
    marginVertical: 24,
  },
  timerContainer: {
    marginBottom: 32,
  },
  timerGradient: {
    width: width - 80,
    height: width - 80,
    borderRadius: (width - 80) / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerInner: {
    width: '85%',
    height: '85%',
    backgroundColor: '#FFFFFF',
    borderRadius: (width - 120) / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerText: {
    fontSize: 52,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  timerLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 16,
  },
  progressRing: {
    width: '80%',
    alignItems: 'center',
  },
  progressTrack: {
    width: '100%',
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  timerControls: {
    alignItems: 'center',
  },
  startButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  startGradient: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  startText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  activeControls: {
    flexDirection: 'row',
    gap: 20,
  },
  controlButton: {
    width: 64,
    height: 64,
    backgroundColor: '#6B46C1',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stopButton: {
    width: 64,
    height: 64,
    backgroundColor: '#EF4444',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  soundsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  soundGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  soundCard: {
    flex: 1,
    minWidth: (width - 72) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  soundCardActive: {
    borderColor: '#6B46C1',
    backgroundColor: '#F3F4F6',
  },
  soundIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  soundName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  soundNameActive: {
    color: '#6B46C1',
  },
  activeBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    backgroundColor: '#6B46C1',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blockingSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  blockingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  blockingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  blockingTitle: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  statusBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  blockingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  blockedApps: {
    backgroundColor: '#FEF2F2',
    borderRadius: 12,
    padding: 12,
  },
  blockedTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#DC2626',
    marginBottom: 8,
  },
  blockedList: {
    gap: 4,
  },
  blockedApp: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#7F1D1D',
  },
  breakSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  activityGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  activityCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
  },
  tipsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  tipCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
});